# 仓位上报客户端Demo

这是一个完整的仓位上报客户端demo，展示了如何向仓位监控系统上报交易数据。

## 📁 文件结构

```
├── position_client_demo.py     # 主要客户端类和演示功能
├── quick_start_example.py      # 快速开始示例
├── test_client_demo.py         # 客户端功能测试
├── CLIENT_USAGE.md             # 详细使用说明
└── README_CLIENT_DEMO.md       # 本文件
```

## 🚀 快速开始

### 1. 启动服务器
```bash
python app.py
```

### 2. 运行快速示例
```bash
python quick_start_example.py
```

### 3. 查看结果
在浏览器中访问 `http://localhost:5000` 查看上报的仓位数据

## 🎯 主要功能

### ✅ 支持的操作

- **开仓上报**: 开始新的仓位建立过程
- **仓位更新**: 更新现有仓位的数量和状态  
- **LONG仓位上报**: 直接上报多头仓位
- **SHORT仓位上报**: 直接上报空头仓位
- **获取仓位数据**: 查询所有当前仓位
- **服务器状态检查**: 验证连接状态

### 📊 支持的数据

- **交易所**: binance, okx, bybit 等
- **交易对**: BTCUSDT, ETHUSDT 等
- **仓位方向**: LONG (多头), SHORT (空头)
- **价格数据**: 开仓价格、平均价格、当前价格
- **数量数据**: 目标数量、当前数量

## 💡 使用示例

### 基础用法

```python
from position_client_demo import PositionReportClient

# 创建客户端
client = PositionReportClient("http://localhost:5000")

# 上报LONG仓位
client.report_long_position(
    exchange="binance",
    symbol="BTCUSDT", 
    open_price=50000,
    quantity=1.0
)

# 上报SHORT仓位
client.report_short_position(
    exchange="okx",
    symbol="ETHUSDT",
    open_price=3200,
    quantity=5.0
)
```

### 开仓流程

```python
# 1. 开始开仓
client.open_position(
    exchange="binance",
    symbol="BTCUSDT",
    position_side="LONG",
    open_price=50000,
    target_quantity=2.0
)

# 2. 更新建仓进度
client.update_position(
    exchange="binance",
    symbol="BTCUSDT", 
    position_side="LONG",
    open_price=50000,
    target_quantity=2.0,
    current_quantity=1.0  # 50%进度
)

# 3. 完成建仓
client.update_position(
    exchange="binance",
    symbol="BTCUSDT",
    position_side="LONG", 
    open_price=50000,
    target_quantity=2.0,
    current_quantity=2.0  # 100%完成
)
```

## 🧪 测试和演示

### 运行完整演示
```bash
python position_client_demo.py
```

选择演示模式:
1. 基础使用演示
2. 逐步开仓演示  
3. SHORT仓位周期演示
4. 全部演示

### 运行功能测试
```bash
python test_client_demo.py
```

测试内容:
- 服务器连接测试
- LONG/SHORT仓位上报测试
- 开仓流程测试
- 数据获取测试
- 错误处理测试
- 性能测试

## 📈 监控功能

系统会自动追踪以下时间指标:

- **开仓耗时**: 从开始开仓到达到95%的时间
- **持仓时间**: 仓位在95%以上的持续时间
- **清仓耗时**: 从95%以下降到5%的时间

这些指标会在Web界面实时显示和更新。

## 🔧 配置说明

### 服务器地址配置
```python
# 本地服务器
client = PositionReportClient("http://localhost:5000")

# 远程服务器  
client = PositionReportClient("http://your-server-ip:5000")
```

### 数据格式
所有价格和数量数据会自动转换为字符串格式发送，支持输入:
- 整数: `50000`
- 浮点数: `50000.5` 
- 字符串: `"50000"`

## ⚠️ 注意事项

1. **服务器状态**: 使用前确保服务器已启动
2. **网络连接**: 检查客户端与服务器的网络连通性
3. **数据格式**: 仓位方向必须是 "LONG" 或 "SHORT"
4. **时间同步**: 系统使用本地时间，确保时间准确性
5. **错误处理**: 客户端包含完善的异常处理机制

## 🔗 相关文件

- `CLIENT_USAGE.md` - 详细的API使用说明
- `app.py` - 服务器端代码
- `test_*.py` - 其他测试脚本

## 🎉 开始使用

1. 确保服务器运行: `python app.py`
2. 运行快速示例: `python quick_start_example.py`  
3. 在浏览器查看: `http://localhost:5000`
4. 根据需要修改服务器地址和参数

这个demo提供了完整的仓位上报解决方案，你可以直接使用或根据需要进行定制开发。
