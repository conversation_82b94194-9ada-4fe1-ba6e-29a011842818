#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速开始示例
展示如何快速使用仓位上报客户端
"""

from position_client_demo import PositionReportClient
import time

def quick_example():
    """快速示例"""
    print("🚀 快速开始示例")
    print("=" * 50)
    
    # 1. 创建客户端 (修改为你的服务器地址)
    client = PositionReportClient("http://localhost:5000")
    
    # 2. 检查服务器连接
    if not client.check_server_status():
        print("❌ 请确保服务器已启动")
        return
    
    print("\n📊 开始上报仓位数据...")
    
    # 3. 上报LONG仓位
    print("\n🟢 上报LONG仓位:")
    client.report_long_position(
        exchange="binance",      # 交易所
        symbol="BTCUSDT",       # 交易对
        open_price=50000,       # 开仓价格
        quantity=1.0,           # 仓位数量
        current_price=51000     # 当前价格
    )
    
    time.sleep(1)
    
    # 4. 上报SHORT仓位
    print("\n🔴 上报SHORT仓位:")
    client.report_short_position(
        exchange="okx",         # 交易所
        symbol="ETHUSDT",       # 交易对
        open_price=3200,        # 开仓价格
        quantity=5.0,           # 仓位数量
        current_price=3150      # 当前价格
    )
    
    time.sleep(1)
    
    # 5. 演示开仓过程
    print("\n📈 演示开仓过程:")
    
    # 开始开仓
    client.open_position(
        exchange="bybit",
        symbol="BTCUSDT", 
        position_side="LONG",
        open_price=49000,
        target_quantity=2.0
    )
    
    time.sleep(1)
    
    # 更新仓位 (建仓50%)
    client.update_position(
        exchange="bybit",
        symbol="BTCUSDT",
        position_side="LONG", 
        open_price=49000,
        target_quantity=2.0,
        current_quantity=1.0    # 当前已建仓1.0，进度50%
    )
    
    time.sleep(1)
    
    # 更新仓位 (建仓100%)
    client.update_position(
        exchange="bybit",
        symbol="BTCUSDT",
        position_side="LONG",
        open_price=49000, 
        target_quantity=2.0,
        current_quantity=2.0    # 当前已建仓2.0，进度100%
    )
    
    print("\n✅ 示例完成！")
    print("💡 你可以在浏览器中访问 http://localhost:5000 查看仓位数据")

if __name__ == "__main__":
    quick_example()
