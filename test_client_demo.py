#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户端功能测试脚本
"""

from position_client_demo import PositionReportClient
import time
import json

def test_client_functionality():
    """测试客户端基本功能"""
    print("🧪 测试客户端基本功能")
    print("=" * 50)
    
    # 创建客户端
    client = PositionReportClient("http://localhost:5000")
    
    # 测试服务器连接
    print("1️⃣ 测试服务器连接...")
    if not client.check_server_status():
        print("❌ 服务器连接失败，请确保服务器已启动")
        return False
    
    # 测试LONG仓位上报
    print("\n2️⃣ 测试LONG仓位上报...")
    success = client.report_long_position(
        exchange="test_exchange",
        symbol="TESTUSDT",
        open_price=100,
        quantity=1.0,
        current_price=105
    )
    if not success:
        print("❌ LONG仓位上报失败")
        return False
    
    time.sleep(1)
    
    # 测试SHORT仓位上报
    print("\n3️⃣ 测试SHORT仓位上报...")
    success = client.report_short_position(
        exchange="test_exchange",
        symbol="TESTUSDT",
        open_price=100,
        quantity=2.0,
        current_price=95
    )
    if not success:
        print("❌ SHORT仓位上报失败")
        return False
    
    time.sleep(1)
    
    # 测试开仓流程
    print("\n4️⃣ 测试开仓流程...")
    
    # 开始开仓
    success = client.open_position(
        exchange="test_exchange",
        symbol="BTCTEST",
        position_side="LONG",
        open_price=50000,
        target_quantity=1.0
    )
    if not success:
        print("❌ 开仓失败")
        return False
    
    time.sleep(1)
    
    # 更新仓位到50%
    success = client.update_position(
        exchange="test_exchange",
        symbol="BTCTEST",
        position_side="LONG",
        open_price=50000,
        target_quantity=1.0,
        current_quantity=0.5
    )
    if not success:
        print("❌ 仓位更新失败")
        return False
    
    time.sleep(1)
    
    # 更新仓位到100%
    success = client.update_position(
        exchange="test_exchange",
        symbol="BTCTEST",
        position_side="LONG",
        open_price=50000,
        target_quantity=1.0,
        current_quantity=1.0
    )
    if not success:
        print("❌ 仓位更新失败")
        return False
    
    time.sleep(1)
    
    # 测试获取仓位数据
    print("\n5️⃣ 测试获取仓位数据...")
    positions = client.get_all_positions()
    if positions is None:
        print("❌ 获取仓位数据失败")
        return False
    
    print(f"✅ 获取到仓位数据: {len(positions)} 个交易所")
    
    # 验证数据
    if "test_exchange" in positions:
        test_data = positions["test_exchange"]
        print(f"   test_exchange: {len(test_data)} 个交易对")
        
        if "TESTUSDT" in test_data:
            testusdt_data = test_data["TESTUSDT"]
            print(f"   TESTUSDT: {list(testusdt_data.keys())} 仓位")
        
        if "BTCTEST" in test_data:
            btctest_data = test_data["BTCTEST"]
            print(f"   BTCTEST: {list(btctest_data.keys())} 仓位")
    
    print("\n✅ 所有测试通过！")
    return True

def test_error_handling():
    """测试错误处理"""
    print("\n🛡️ 测试错误处理")
    print("=" * 50)
    
    # 测试错误的服务器地址
    print("1️⃣ 测试错误的服务器地址...")
    bad_client = PositionReportClient("http://invalid-server:9999")
    if bad_client.check_server_status():
        print("❌ 应该连接失败但却成功了")
    else:
        print("✅ 正确处理了连接失败")
    
    # 测试无效数据
    print("\n2️⃣ 测试数据验证...")
    client = PositionReportClient("http://localhost:5000")
    
    # 这里可以添加更多的错误处理测试
    print("✅ 错误处理测试完成")

def performance_test():
    """性能测试"""
    print("\n⚡ 性能测试")
    print("=" * 50)
    
    client = PositionReportClient("http://localhost:5000")
    
    if not client.check_server_status():
        print("❌ 服务器连接失败")
        return
    
    # 测试批量上报
    print("测试批量上报性能...")
    start_time = time.time()
    
    for i in range(10):
        client.report_long_position(
            exchange=f"exchange_{i % 3}",
            symbol=f"SYMBOL{i}USDT",
            open_price=1000 + i * 10,
            quantity=1.0 + i * 0.1
        )
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"✅ 10次上报耗时: {duration:.2f}秒")
    print(f"   平均每次: {duration/10:.3f}秒")

def main():
    """主测试函数"""
    print("🚀 仓位上报客户端测试")
    print("=" * 60)
    
    try:
        # 基本功能测试
        if not test_client_functionality():
            print("❌ 基本功能测试失败")
            return
        
        # 错误处理测试
        test_error_handling()
        
        # 性能测试
        performance_test()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        print("💡 你可以访问 http://localhost:5000 查看上报的数据")
        
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
