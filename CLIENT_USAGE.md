# 仓位上报客户端使用说明

## 概述

这个仓位上报客户端demo展示了如何向仓位监控系统上报交易数据，包括开仓、更新仓位、long/short仓位上报等功能。

## 文件说明

- `position_client_demo.py` - 主要的客户端类和演示功能
- `quick_start_example.py` - 快速开始示例
- `CLIENT_USAGE.md` - 本使用说明文档

## 快速开始

### 1. 启动服务器

```bash
python app.py
```

### 2. 运行快速示例

```bash
python quick_start_example.py
```

### 3. 运行完整演示

```bash
python position_client_demo.py
```

## 核心功能

### PositionReportClient 类

主要的客户端类，提供以下功能：

#### 初始化
```python
from position_client_demo import PositionReportClient

# 创建客户端实例
client = PositionReportClient("http://localhost:5000")
```

#### 检查服务器状态
```python
if client.check_server_status():
    print("服务器连接正常")
```

### 仓位上报方法

#### 1. 上报LONG仓位
```python
client.report_long_position(
    exchange="binance",      # 交易所名称
    symbol="BTCUSDT",       # 交易对
    open_price=50000,       # 开仓价格
    quantity=1.0,           # 仓位数量
    current_price=51000     # 当前价格 (可选)
)
```

#### 2. 上报SHORT仓位
```python
client.report_short_position(
    exchange="okx",         # 交易所名称
    symbol="ETHUSDT",       # 交易对
    open_price=3200,        # 开仓价格
    quantity=5.0,           # 仓位数量
    current_price=3150      # 当前价格 (可选)
)
```

#### 3. 开仓流程

**步骤1: 开始开仓**
```python
client.open_position(
    exchange="binance",
    symbol="BTCUSDT",
    position_side="LONG",   # LONG 或 SHORT
    open_price=50000,
    target_quantity=2.0     # 目标数量
)
```

**步骤2: 更新仓位**
```python
client.update_position(
    exchange="binance",
    symbol="BTCUSDT", 
    position_side="LONG",
    open_price=50000,
    target_quantity=2.0,
    current_quantity=1.0    # 当前已建仓数量
)
```

#### 4. 获取所有仓位
```python
positions = client.get_all_positions()
if positions:
    print("获取仓位成功:", positions)
```

## 数据格式说明

### 请求数据格式
```json
{
    "exchange": "binance",           // 交易所名称
    "symbol": "BTCUSDT",            // 交易对
    "action": "update_pos",         // 动作: open_pos(开仓) 或 update_pos(更新)
    "position": {
        "time": "2025-01-28 10:30:00",      // 时间戳
        "open_price": "50000",              // 开仓价格
        "open_quantity": "2.0",             // 目标数量
        "open_avg_price": "50000",          // 平均开仓价格
        "current_quantity": "1.0",          // 当前数量
        "position_side": "LONG",            // 仓位方向: LONG 或 SHORT
        "current_price": "51000"            // 当前价格
    }
}
```

### 响应数据格式
```json
{
    "status": "success",
    "message": "数据接收成功"
}
```

## 支持的交易所

- binance
- okx  
- bybit
- 其他自定义交易所名称

## 支持的交易对

- BTCUSDT
- ETHUSDT
- 其他自定义交易对

## 仓位方向

- **LONG**: 多头仓位
- **SHORT**: 空头仓位

## 动作类型

- **open_pos**: 开始开仓，会重置时间记录
- **update_pos**: 更新仓位，用于建仓过程中的进度更新

## 时间追踪功能

系统会自动追踪以下时间指标：

1. **开仓耗时**: 从开始开仓到仓位达到95%的时间
2. **持仓时间**: 仓位在95%以上的时间
3. **清仓耗时**: 从仓位降到95%以下到降到5%的时间

## 实时监控

访问 `http://localhost:5000` 可以在浏览器中实时查看：
- 所有仓位数据
- 时间指标
- 仓位进度
- 实时更新

## 错误处理

客户端包含完善的错误处理：
- 网络连接异常
- 服务器响应错误
- 数据格式错误
- 超时处理

## 示例场景

### 场景1: 简单上报
直接上报已有的仓位数据

### 场景2: 逐步建仓
模拟真实的开仓过程，从0%到100%

### 场景3: 完整周期
演示开仓-持仓-平仓的完整生命周期

## 注意事项

1. 确保服务器已启动
2. 检查网络连接
3. 价格和数量使用数字类型
4. 仓位方向必须是 LONG 或 SHORT
5. 时间格式为 "YYYY-MM-DD HH:MM:SS"

## 扩展使用

你可以基于这个客户端类开发自己的交易系统集成：

```python
# 集成到你的交易系统
class MyTradingSystem:
    def __init__(self):
        self.position_client = PositionReportClient("http://your-server:5000")
    
    def on_position_change(self, exchange, symbol, side, price, quantity):
        # 当仓位发生变化时上报
        self.position_client.update_position(
            exchange=exchange,
            symbol=symbol,
            position_side=side,
            open_price=price,
            target_quantity=quantity,
            current_quantity=quantity
        )
```
