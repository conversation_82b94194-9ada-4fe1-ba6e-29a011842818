#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上报客户端Demo
演示如何使用客户端上报仓位数据，包括开仓、更新仓位、long/short仓位上报
"""

import requests
import json
import time
import random
from datetime import datetime
from typing import Optional, Dict, Any


class PositionReportClient:
    """仓位上报客户端"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        """
        初始化客户端
        
        Args:
            base_url: 服务器地址
        """
        self.base_url = base_url
        self.api_url = f"{base_url}/api/position"
        self.session = requests.Session()
        
    def _send_request(self, data: Dict[str, Any]) -> bool:
        """
        发送请求到服务器
        
        Args:
            data: 要发送的数据
            
        Returns:
            bool: 是否发送成功
        """
        try:
            response = self.session.post(self.api_url, json=data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 上报成功: {result.get('message', '数据接收成功')}")
                return True
            else:
                print(f"❌ 上报失败: {response.status_code} - {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求异常: {e}")
            return False
        except Exception as e:
            print(f"❌ 未知异常: {e}")
            return False
    
    def open_position(self, exchange: str, symbol: str, position_side: str, 
                     open_price: float, target_quantity: float, 
                     current_price: Optional[float] = None) -> bool:
        """
        开始开仓
        
        Args:
            exchange: 交易所名称 (如: binance, okx, bybit)
            symbol: 交易对 (如: BTCUSDT, ETHUSDT)
            position_side: 仓位方向 (LONG 或 SHORT)
            open_price: 开仓价格
            target_quantity: 目标数量
            current_price: 当前价格 (可选，默认使用开仓价格)
            
        Returns:
            bool: 是否开仓成功
        """
        if current_price is None:
            current_price = open_price
            
        data = {
            "exchange": exchange,
            "symbol": symbol,
            "action": "open_pos",  # 开仓动作
            "position": {
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "open_price": str(open_price),
                "open_quantity": str(target_quantity),
                "open_avg_price": str(open_price),
                "current_quantity": "0",  # 开仓时当前数量为0
                "position_side": position_side.upper(),
                "current_price": str(current_price)
            }
        }
        
        print(f"🚀 开始开仓: {exchange} {symbol} {position_side} - 目标数量: {target_quantity}")
        return self._send_request(data)
    
    def update_position(self, exchange: str, symbol: str, position_side: str,
                       open_price: float, target_quantity: float, 
                       current_quantity: float, avg_price: Optional[float] = None,
                       current_price: Optional[float] = None) -> bool:
        """
        更新仓位
        
        Args:
            exchange: 交易所名称
            symbol: 交易对
            position_side: 仓位方向 (LONG 或 SHORT)
            open_price: 开仓价格
            target_quantity: 目标数量
            current_quantity: 当前数量
            avg_price: 平均价格 (可选，默认使用开仓价格)
            current_price: 当前价格 (可选，默认使用开仓价格)
            
        Returns:
            bool: 是否更新成功
        """
        if avg_price is None:
            avg_price = open_price
        if current_price is None:
            current_price = open_price + random.uniform(-100, 100)  # 模拟价格波动
            
        data = {
            "exchange": exchange,
            "symbol": symbol,
            "action": "update_pos",  # 更新动作
            "position": {
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "open_price": str(open_price),
                "open_quantity": str(target_quantity),
                "open_avg_price": str(avg_price),
                "current_quantity": str(current_quantity),
                "position_side": position_side.upper(),
                "current_price": str(current_price)
            }
        }
        
        progress = (current_quantity / target_quantity) * 100 if target_quantity > 0 else 0
        print(f"📊 更新仓位: {exchange} {symbol} {position_side} - 进度: {progress:.1f}% ({current_quantity}/{target_quantity})")
        return self._send_request(data)
    
    def report_long_position(self, exchange: str, symbol: str, 
                           open_price: float, quantity: float,
                           avg_price: Optional[float] = None,
                           current_price: Optional[float] = None) -> bool:
        """
        上报多头(LONG)仓位
        
        Args:
            exchange: 交易所名称
            symbol: 交易对
            open_price: 开仓价格
            quantity: 仓位数量
            avg_price: 平均价格
            current_price: 当前价格
            
        Returns:
            bool: 是否上报成功
        """
        return self.update_position(
            exchange=exchange,
            symbol=symbol,
            position_side="LONG",
            open_price=open_price,
            target_quantity=quantity,
            current_quantity=quantity,
            avg_price=avg_price,
            current_price=current_price
        )
    
    def report_short_position(self, exchange: str, symbol: str,
                            open_price: float, quantity: float,
                            avg_price: Optional[float] = None,
                            current_price: Optional[float] = None) -> bool:
        """
        上报空头(SHORT)仓位
        
        Args:
            exchange: 交易所名称
            symbol: 交易对
            open_price: 开仓价格
            quantity: 仓位数量
            avg_price: 平均价格
            current_price: 当前价格
            
        Returns:
            bool: 是否上报成功
        """
        return self.update_position(
            exchange=exchange,
            symbol=symbol,
            position_side="SHORT",
            open_price=open_price,
            target_quantity=quantity,
            current_quantity=quantity,
            avg_price=avg_price,
            current_price=current_price
        )
    
    def get_all_positions(self) -> Optional[Dict]:
        """
        获取所有仓位数据
        
        Returns:
            Dict: 所有仓位数据，失败时返回None
        """
        try:
            response = self.session.get(f"{self.base_url}/api/positions", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 获取仓位数据失败: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ 获取仓位数据异常: {e}")
            return None
    
    def check_server_status(self) -> bool:
        """
        检查服务器状态
        
        Returns:
            bool: 服务器是否正常
        """
        try:
            response = self.session.get(self.base_url, timeout=5)
            if response.status_code == 200:
                print("✅ 服务器连接正常")
                return True
            else:
                print(f"❌ 服务器响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 无法连接到服务器: {e}")
            return False


def demo_basic_usage():
    """基础使用演示"""
    print("=" * 60)
    print("🎯 基础使用演示")
    print("=" * 60)
    
    # 创建客户端实例
    client = PositionReportClient("http://localhost:5000")
    
    # 检查服务器状态
    if not client.check_server_status():
        print("请确保服务器已启动 (python app.py)")
        return
    
    # 1. 上报LONG仓位
    print("\n1️⃣ 上报LONG仓位")
    client.report_long_position(
        exchange="binance",
        symbol="BTCUSDT", 
        open_price=50000,
        quantity=1.5,
        current_price=51000
    )
    
    time.sleep(1)
    
    # 2. 上报SHORT仓位
    print("\n2️⃣ 上报SHORT仓位")
    client.report_short_position(
        exchange="okx",
        symbol="ETHUSDT",
        open_price=3200,
        quantity=5.0,
        current_price=3150
    )
    
    time.sleep(1)
    
    # 3. 获取所有仓位
    print("\n3️⃣ 获取所有仓位数据")
    positions = client.get_all_positions()
    if positions:
        print(f"📊 当前仓位数量: {len(positions)} 个交易所")
        for exchange, symbols in positions.items():
            print(f"  {exchange}: {len(symbols)} 个交易对")


def demo_progressive_opening():
    """逐步开仓演示"""
    print("=" * 60)
    print("📈 逐步开仓演示")
    print("=" * 60)
    
    client = PositionReportClient("http://localhost:5000")
    
    if not client.check_server_status():
        return
    
    exchange = "binance"
    symbol = "BTCUSDT"
    position_side = "LONG"
    open_price = 50000
    target_quantity = 2.0
    
    # 1. 开始开仓
    print("1️⃣ 开始开仓")
    client.open_position(exchange, symbol, position_side, open_price, target_quantity)
    time.sleep(1)
    
    # 2. 逐步建仓
    print("\n2️⃣ 逐步建仓过程")
    steps = 10
    for i in range(1, steps + 1):
        current_quantity = (target_quantity / steps) * i
        avg_price = open_price + random.uniform(-50, 50)  # 模拟平均价格变化
        
        client.update_position(
            exchange=exchange,
            symbol=symbol,
            position_side=position_side,
            open_price=open_price,
            target_quantity=target_quantity,
            current_quantity=current_quantity,
            avg_price=avg_price
        )
        time.sleep(0.8)
    
    print("✅ 开仓完成")


def demo_short_position_cycle():
    """SHORT仓位完整周期演示"""
    print("=" * 60)
    print("📉 SHORT仓位完整周期演示")
    print("=" * 60)
    
    client = PositionReportClient("http://localhost:5000")
    
    if not client.check_server_status():
        return
    
    exchange = "bybit"
    symbol = "ETHUSDT"
    position_side = "SHORT"
    open_price = 3200
    target_quantity = 3.0
    
    # 1. 开始开空仓
    print("1️⃣ 开始开空仓")
    client.open_position(exchange, symbol, position_side, open_price, target_quantity)
    time.sleep(1)
    
    # 2. 逐步建空仓到95%
    print("\n2️⃣ 逐步建空仓")
    for i in range(1, 11):
        current_quantity = (target_quantity / 10) * i
        progress = (current_quantity / target_quantity) * 100
        
        client.update_position(
            exchange=exchange,
            symbol=symbol,
            position_side=position_side,
            open_price=open_price,
            target_quantity=target_quantity,
            current_quantity=current_quantity
        )
        
        if progress >= 95:
            print("🎯 空仓达到95%，开始持仓阶段")
            break
            
        time.sleep(0.5)
    
    # 3. 持仓阶段
    print("\n3️⃣ 持仓阶段")
    for i in range(3):
        print(f"   持仓中... {i+1}/3")
        time.sleep(1)
    
    # 4. 逐步平仓
    print("\n4️⃣ 逐步平仓")
    for i in range(9, 0, -1):
        current_quantity = (target_quantity / 10) * i
        progress = (current_quantity / target_quantity) * 100
        
        client.update_position(
            exchange=exchange,
            symbol=symbol,
            position_side=position_side,
            open_price=open_price,
            target_quantity=target_quantity,
            current_quantity=current_quantity
        )
        
        if progress <= 5:
            print("🎯 平仓完成")
            break
            
        time.sleep(0.5)
    
    print("✅ SHORT仓位周期完成")


def main():
    """主函数"""
    print("🚀 仓位上报客户端Demo")
    print("=" * 60)
    print("演示功能:")
    print("1. 基础使用 - 直接上报LONG/SHORT仓位")
    print("2. 逐步开仓 - 演示完整的开仓过程")
    print("3. SHORT仓位周期 - 演示空仓的完整生命周期")
    print("4. 全部演示")
    print("=" * 60)
    
    try:
        choice = input("请选择演示模式 (1-4): ").strip()
        
        if choice == "1":
            demo_basic_usage()
        elif choice == "2":
            demo_progressive_opening()
        elif choice == "3":
            demo_short_position_cycle()
        elif choice == "4":
            demo_basic_usage()
            print("\n")
            demo_progressive_opening()
            print("\n")
            demo_short_position_cycle()
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 演示已取消")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")


if __name__ == "__main__":
    main()
